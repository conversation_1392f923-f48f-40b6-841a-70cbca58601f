// Background script for handling messages between content script and popup
// 导入OTPAuth库
importScripts('libs/otpauth.umd.min.js');

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getMatchingOTP') {
    // 获取存储的密钥数据
    chrome.storage.sync.get('twoFaKeys', (result) => {
      const keysData = result.twoFaKeys || [];
      const currentDomain = request.domain;

      // 查找最匹配的密钥（优先级：完全匹配 > 包含匹配）
      const matchedKey = findBestMatchingKey(keysData, currentDomain);

      if (matchedKey) {
        // 使用与popup.js相同的OTP计算方法
        const otp = calculateOTP(matchedKey.secret);
        sendResponse({
          success: true,
          otp: otp,
          keyword: matchedKey.keyword
        });
      } else {
        sendResponse({
          success: false,
          message: '未找到匹配的2FA密钥'
        });
      }
    });
    return true; // 保持消息通道开放
  }
});

// 查找最匹配的密钥
function findBestMatchingKey(keysData, currentDomain) {
  const candidates = [];

  // 收集所有可能匹配的密钥
  keysData.forEach(key => {
    const keyword = key.keyword.toLowerCase();
    const domain = currentDomain.toLowerCase();

    // 检查是否匹配
    if (domain.includes(keyword)) {
      let score = 0;

      // 计算匹配分数
      if (domain === keyword) {
        // 完全匹配：最高优先级
        score = 1000;
      } else if (keyword.includes(domain)) {
        // 关键词包含域名：高优先级
        score = 500;
      } else if (domain.includes(keyword)) {
        // 域名包含关键词：基础优先级
        score = 100 + keyword.length; // 关键词越长，匹配度越高
      }

      candidates.push({
        key: key,
        score: score,
        keyword: keyword
      });
    }
  });

  // 如果没有匹配的候选项
  if (candidates.length === 0) {
    return null;
  }

  // 按分数排序，返回最高分的密钥
  candidates.sort((a, b) => b.score - a.score);

  console.log('匹配候选项:', candidates.map(c => ({
    keyword: c.keyword,
    score: c.score
  })));

  return candidates[0].key;
}

// 与popup.js中完全相同的OTP计算函数
function calculateOTP(secret) {
  try {
    const totp = new OTPAuth.TOTP({
      secret: OTPAuth.Secret.fromBase32(secret),
      digits: 6,
      period: 30
    });
    return totp.generate();
  } catch (error) {
    console.error('计算OTP失败:', error);
    return '无效密钥';
  }
}
