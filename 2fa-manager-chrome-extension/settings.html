<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>2FA 管理器 - 设置</title>
    <style>
        html, body {
            width: 550px;
            height: 500px;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Robot<PERSON>, Oxygen, Ubuntu, sans-serif;
            background-color: #f5f5f7;
            color: #1d1d1f;
            overflow: hidden;
            box-sizing: border-box;
        }

        .container {
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e2e7;
        }

        .back-btn {
            background-color: #f5f5f7;
            color: #1d1d1f;
            border: none;
            border-radius: 6px;
            padding: 8px 10px;
            margin-right: 15px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
        }

        .back-btn:hover {
            background-color: #ebebeb;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .header h1 i {
            margin-right: 8px;
            color: #0071e3;
        }

        .settings-content {
            flex: 1;
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .setting-section {
            margin-bottom: 30px;
        }

        .setting-section:last-child {
            margin-bottom: 0;
        }

        .setting-section h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .setting-section p {
            margin: 0 0 15px 0;
            font-size: 14px;
            color: #6e6e73;
            line-height: 1.5;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        button {
            background-color: #0071e3;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        button:hover {
            background-color: #0077ed;
        }

        .secondary-btn {
            background-color: #f5f5f7;
            color: #1d1d1f;
        }

        .secondary-btn:hover {
            background-color: #ebebeb;
        }

        .danger-btn {
            background-color: #ff3b30;
            color: white;
        }

        .danger-btn:hover {
            background-color: #d70015;
        }

        .file-input {
            display: none;
        }

        .status-message {
            margin-top: 15px;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            display: none;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .divider {
            height: 1px;
            background-color: #e2e2e7;
            margin: 25px 0;
        }

        .export-preview {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" id="backBtn" title="返回">
                <i class="fa fa-arrow-left"></i>
            </button>
            <h1><i class="fa fa-cog"></i>设置</h1>
        </div>

        <div class="settings-content">
            <div class="setting-section">
                <h3>数据导出</h3>
                <p>导出所有2FA密钥配置数据，可用于备份或迁移到其他设备。</p>
                <div class="button-group">
                    <button id="exportBtn">
                        <i class="fa fa-download"></i>
                        导出配置
                    </button>
                </div>
                <div class="export-preview" id="exportPreview" style="display: none;"></div>
            </div>

            <div class="divider"></div>

            <div class="setting-section">
                <h3>数据导入</h3>
                <p>从备份文件导入2FA密钥配置。如果导入的数据与现有数据完全相同，将自动跳过重复项。</p>
                <div class="button-group">
                    <button id="importBtn" class="secondary-btn">
                        <i class="fa fa-upload"></i>
                        选择文件导入
                    </button>
                    <input type="file" id="importFile" class="file-input" accept=".json,.txt">
                </div>
            </div>

            <div class="divider"></div>

            <div class="setting-section">
                <h3>危险操作</h3>
                <p>清除所有数据将删除所有已保存的2FA密钥，此操作不可恢复。</p>
                <div class="button-group">
                    <button id="clearAllBtn" class="danger-btn">
                        <i class="fa fa-trash"></i>
                        清除所有数据
                    </button>
                </div>
            </div>

            <div class="status-message" id="statusMessage"></div>
        </div>
    </div>

    <!-- 本地资源引用 -->
    <link rel="stylesheet" href="libs/font-awesome.min.css">
    <script src="settings.js"></script>
</body>
</html>
