// Content script for auto-filling 2FA codes on supported domains
(function() {
  'use strict';

  // 支持的域名和路径配置
  const supportedSites = {
    'jumpserver.dc.servyou-it.com': {
      paths: ['/core/auth/login/mfa/'],
      selector: '.mfa-div input[name="code"]'
    },
    'signin.aliyun.com': {
      paths: ['/login.htm','/servyou01/login.htm#/main'],
      selector: 'input[placeholder="请输入 6 位数字安全码"]'
    },
    'gitlab.dc.servyou-it.com': {
      paths: ['/users/auth/ldapmain/callback'],
      selector: 'input[name="user[otp_attempt]"]'
    },
    'gd-gitlab.dc.servyou-it.com': {
      paths: ['/users/auth/ldapmain/callback'],
      selector: 'input[name="user[otp_attempt]"]'
    },
    'devops.dc.servyou-it.com': {
      paths: ['/wx/login'],
      selector: 'input#code[placeholder="请输入二次认证密码"], input#code'
    }
  };

  // 检查当前页面是否需要2FA填充
  function shouldFillMFA() {
    const hostname = window.location.hostname;
    const pathname = window.location.pathname;
    const search = window.location.search;

    console.log('检查页面:', { hostname, pathname, search, fullUrl: window.location.href });

    const siteConfig = supportedSites[hostname];
    if (!siteConfig) {
      console.log('不支持的域名:', hostname);
      return false;
    }

    // 如果paths为空数组，则只根据hostname匹配
    if (siteConfig.paths.length === 0) {
      console.log('paths为空，仅根据hostname匹配，执行2FA填充');
      return true;
    }

    // 检查路径是否匹配
    const isPathMatch = siteConfig.paths.some(path => {
      const matches = pathname.startsWith(path);
      console.log(`路径匹配检查: ${pathname} startsWith ${path} = ${matches}`);
      return matches;
    });

    if (!isPathMatch) {
      console.log('路径不匹配，跳过2FA填充');
      return false;
    }

    console.log('页面匹配，可以执行2FA填充');
    return true;
  }

  // 检查是否应该执行2FA填充
  if (!shouldFillMFA()) {
    return;
  }

  // 获取当前域名对应的MFA输入框选择器
  function getMFAInputSelector() {
    const hostname = window.location.hostname;
    const siteConfig = supportedSites[hostname];
    return siteConfig ? siteConfig.selector : null;
  }

  // 等待页面加载完成
  function waitForElement(selector, timeout = 15000) {
    return new Promise((resolve, reject) => {
      console.log('开始查找元素:', selector);

      const element = document.querySelector(selector);
      if (element) {
        console.log('立即找到元素:', element);
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector);
        if (element) {
          console.log('通过观察器找到元素:', element);
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        console.log('查找元素超时:', selector);
        console.log('当前页面所有input元素:', document.querySelectorAll('input'));
        reject(new Error('Element not found within timeout'));
      }, timeout);
    });
  }

  // 获取匹配的OTP
  function getMatchingOTP() {
    return new Promise((resolve) => {
      const currentDomain = window.location.hostname.toLowerCase();

      chrome.runtime.sendMessage({
        action: 'getMatchingOTP',
        domain: currentDomain
      }, (response) => {
        resolve(response);
      });
    });
  }

  // DevOps专用的元素查找函数
  function findDevOpsInput() {
    // 尝试多种选择器，优先使用更精确的选择器
    const selectors = [
      'input#code[placeholder="请输入二次认证密码"]', // 最精确的选择器
      'input#code',
      'input[placeholder="请输入二次认证密码"]:not([id="username"])', // 排除用户名输入框
      '.devops-new-input-affix-wrapper input#code',
      'input[type="text"][placeholder*="认证"]:not([placeholder*="用户"])',
      'input[type="text"][placeholder*="密码"]:not([placeholder*="用户"])'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        // 额外验证：确保不是用户名输入框
        if (element.id === 'username' || element.placeholder.includes('用户名')) {
          console.log('跳过用户名输入框:', selector, element);
          continue;
        }
        console.log('找到DevOps输入框，使用选择器:', selector, element);
        return element;
      }
    }

    console.log('未找到DevOps输入框，尝试所有input元素:');
    const allInputs = document.querySelectorAll('input[type="text"]');
    allInputs.forEach((input, index) => {
      console.log(`Input ${index}:`, {
        placeholder: input.placeholder,
        id: input.id,
        className: input.className,
        element: input
      });
    });

    return null;
  }

  // 填充2FA验证码
  async function fillMFACode() {
    try {
      let mfaInput;

      // 对DevOps使用专门的查找逻辑
      if (window.location.hostname === 'devops.dc.servyou-it.com') {
        // 先尝试直接查找
        mfaInput = findDevOpsInput();

        // 如果没找到，等待一段时间后再试
        if (!mfaInput) {
          console.log('DevOps输入框未立即找到，等待页面加载...');
          await new Promise(resolve => setTimeout(resolve, 2000));
          mfaInput = findDevOpsInput();
        }

        if (!mfaInput) {
          console.log('DevOps输入框最终未找到');
          return;
        }
      } else {
        // 其他网站使用原有逻辑
        const selector = getMFAInputSelector();
        if (!selector) {
          console.log('当前域名不支持自动填充');
          return;
        }

        // 等待MFA输入框出现
        mfaInput = await waitForElement(selector);

        if (!mfaInput) {
          console.log('未找到MFA输入框:', selector);
          return;
        }
      }

      // 获取匹配的OTP
      const result = await getMatchingOTP();

      if (!result || !result.success) {
        console.log('未找到匹配的2FA密钥');
        showNotification('未找到匹配的2FA密钥', 'info');
        return;
      }

      // 填充验证码
      mfaInput.value = result.otp;

      // 触发input事件，确保页面能检测到值的变化
      mfaInput.dispatchEvent(new Event('input', { bubbles: true }));
      mfaInput.dispatchEvent(new Event('change', { bubbles: true }));

      // 显示成功提示
      let siteName;
      switch (window.location.hostname) {
        case 'signin.aliyun.com':
          siteName = '阿里云';
          break;
        case 'gitlab.dc.servyou-it.com':
          siteName = 'GitLab';
          break;
        case 'gd-gitlab.dc.servyou-it.com':
          siteName = 'GD-GitLab';
          break;
        case 'devops.dc.servyou-it.com':
          siteName = 'DevOps';
          break;
        default:
          siteName = 'JumpServer';
      }
      showNotification(`已为${siteName}自动填充 ${result.keyword} 的验证码`, 'success');

      console.log('2FA验证码已自动填充:', result.otp, '来源:', result.keyword, '网站:', siteName);

    } catch (error) {
      console.log('自动填充2FA验证码失败:', error);
      showNotification('自动填充失败', 'error');
    }
  }

  // 显示通知
  function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      opacity: 0;
      transform: translateX(100%);
    `;
    
    // 根据类型设置颜色
    switch (type) {
      case 'success':
        notification.style.backgroundColor = '#34c759';
        break;
      case 'error':
        notification.style.backgroundColor = '#ff3b30';
        break;
      default:
        notification.style.backgroundColor = '#007aff';
    }
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 3秒后自动消失
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  // 监听页面变化，当MFA输入框出现时自动填充
  function observePageChanges() {
    const selector = getMFAInputSelector();
    if (!selector) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查是否包含MFA输入框
              if (node.querySelector && node.querySelector(selector)) {
                setTimeout(fillMFACode, 500); // 延迟500ms确保元素完全加载
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // 页面加载完成后开始监听
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        fillMFACode();
        observePageChanges();
      }, 1000);
    });
  } else {
    setTimeout(() => {
      fillMFACode();
      observePageChanges();
    }, 1000);
  }

})();
