<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>2FA 密钥管理器</title>
    <style>
        /* 内联所有样式，不引用外部CSS */
        html, body {
            width: 550px;
            height: 500px !important;
            max-height: 500px !important;
            min-height: 500px !important;
            margin: 0;
            padding: 0;
            font-family: 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, sans-serif;
            background-color: #f5f5f7;
            color: #1d1d1f;
            overflow: hidden;
            box-sizing: border-box;
        }

        .container {
            height: 100%;
            padding: 15px 15px 15px 15px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .table-container {
            flex: 1;
            margin-top: 0px 0px 0px 5px;
            display: flex;
            flex-direction: column;
        }

        .table-header {
            background-color: white;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table-body {
            flex: 1;
            overflow-y: auto;
            background-color: white;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            max-height: 350px;
            min-height: 200px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e2e7;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .header h1 i {
            margin-right: 8px;
            color: #0071e3;
        }
        
        button {
            background-color: #0071e3;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background-color: #0077ed;
        }
        
        .header-table, .body-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            table-layout: fixed;
        }

        .header-table {
            border-radius: 10px 10px 0 0;
        }

        .body-table {
            border-radius: 0 0 10px 10px;
        }

        /* 设置列宽 */
        .header-table th:nth-child(1),
        .body-table td:nth-child(1) { width: 20%; }
        .header-table th:nth-child(2),
        .body-table td:nth-child(2) { width: 0; display: none; }
        .header-table th:nth-child(3),
        .body-table td:nth-child(3) { width: 25%; }
        .header-table th:nth-child(4),
        .body-table td:nth-child(4) { width: 15%; }
        .header-table th:nth-child(5),
        .body-table td:nth-child(5) { width: 25%; }
        .header-table th:nth-child(6),
        .body-table td:nth-child(6) { width: 15%; }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 0;
        }
        
        th {
            background-color: #f9f9f9;
            font-weight: 600;
            font-size: 13px;
            color: #6e6e73;
        }
        
        tr:last-child td {
            border-bottom: none;
        }
        
        tr:hover {
            background-color: #f7f7f9;
        }
        
        .otp-value {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            letter-spacing: 2px;
            color: #0071e3;
        }
        
        .expiry {
            color: #ff3b30;
            font-weight: 500;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6e6e73;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background-color: white;
            padding: 25px;
            border-radius: 10px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .modal h2 {
            margin-top: 0;
            font-size: 20px;
            margin-bottom: 20px;
            color: #1d1d1f;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d2d2d7;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #0071e3;
            box-shadow: 0 0 0 2px rgba(0, 113, 227, 0.2);
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 25px;
        }
        
        .form-actions button[type="button"] {
            background-color: #f5f5f7;
            color: #1d1d1f;
        }
        
        .form-actions button[type="button"]:hover {
            background-color: #ebebeb;
        }
        
        textarea {
            min-height: 80px;
            resize: vertical;
            font-family: inherit;
        }

        .delete-btn {
            background-color: #ff3b30;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
            min-width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .delete-btn:hover {
            background-color: #d70015;
        }

        .actions-cell {
            text-align: center;
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
        }

        .delete-modal-content {
            max-width: 350px;
            text-align: center;
        }

        .delete-modal-content h2 {
            color: #ff3b30;
            margin-bottom: 15px;
        }

        .delete-modal-content p {
            margin-bottom: 25px;
            color: #6e6e73;
            line-height: 1.5;
        }

        .delete-confirm-btn {
            background-color: #ff3b30 !important;
            color: white !important;
        }

        .delete-confirm-btn:hover {
            background-color: #d70015 !important;
        }

        /* 悬停时显示完整内容 */
        td:not(.actions-cell) {
            position: relative;
        }

        td:not(.actions-cell):hover {
            overflow: visible;
            white-space: normal;
            background-color: #f7f7f9;
            z-index: 10;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            border-radius: 4px;
        }

        /* 高亮匹配的行 */
        .highlight-row {
            background-color: #e8f4fd !important;
            border-left: 4px solid #0071e3;
        }

        .highlight-row:hover {
            background-color: #d1e9f6 !important;
        }

        /* OTP值可点击样式 */
        .otp-value {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .otp-value:hover {
            background-color: #e8f4fd;
            border-radius: 4px;
        }

        /* 复制成功提示 */
        .copy-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #34c759;
            color: white;
            padding: 10px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .copy-toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        /* 头部按钮组样式 */
        .header-buttons {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .settings-btn {
            background-color: #f5f5f7;
            color: #1d1d1f;
            padding: 8px 10px;
            min-width: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .settings-btn:hover {
            background-color: #ebebeb;
        }

        .settings-btn i {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fa fa-shield"></i>2FA 密钥管理器</h1>
            <div class="header-buttons">
                <button id="settingsBtn" class="settings-btn" title="设置"><i class="fa fa-cog"></i></button>
                <button id="addBtn"><i class="fa fa-plus"></i> 添加新密钥</button>
            </div>
        </div>

        <div class="table-container">
            <div class="table-header">
                <table class="header-table">
                    <thead>
                        <tr>
                            <th>关键词</th>
                            <th style="display: none;">密钥</th>
                            <th>当前动态值</th>
                            <th>有效期(s)</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                </table>
            </div>
            <div class="table-body">
                <table class="body-table">
                    <tbody id="keysList">
                        <!-- 密钥列表将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 编辑/添加模态框 -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <h2 id="modalTitle">添加新密钥</h2>
            <form id="editForm">
                <input type="hidden" id="editId">
                <div class="form-group">
                    <label for="keyword">关键词 *</label>
                    <input type="text" id="keyword" placeholder="例如：GitHub、Google" required>
                </div>
                <div class="form-group">
                    <label for="secret">密钥 (Base32) *</label>
                    <input type="text" id="secret" placeholder="2FA密钥，不含空格" required>
                </div>
                <div class="form-group">
                    <label for="notes">备注</label>
                    <textarea id="notes" placeholder="添加备注信息（可选）"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" id="cancelBtn">取消</button>
                    <button type="submit">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal" id="deleteModal">
        <div class="modal-content delete-modal-content">
            <h2>确认删除</h2>
            <p id="deleteMessage">确定要删除这个密钥吗？</p>
            <div class="form-actions">
                <button type="button" id="deleteCancel">取消</button>
                <button type="button" id="deleteConfirm" class="delete-confirm-btn">删除</button>
            </div>
        </div>
    </div>

    <!-- 复制成功提示 -->
    <div class="copy-toast" id="copyToast">已复制</div>

    <!-- 本地资源引用 -->
    <link rel="stylesheet" href="libs/font-awesome.min.css">
    <script src="libs/otpauth.umd.min.js"></script>
    <script src="popup.js"></script>
</body>
</html>
    