// 设置页面脚本
(function() {
    'use strict';

    // DOM 元素
    const backBtn = document.getElementById('backBtn');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');
    const clearAllBtn = document.getElementById('clearAllBtn');
    const statusMessage = document.getElementById('statusMessage');
    const exportPreview = document.getElementById('exportPreview');

    // 显示状态消息
    function showStatus(message, type = 'info') {
        statusMessage.textContent = message;
        statusMessage.className = `status-message status-${type}`;
        statusMessage.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            statusMessage.style.display = 'none';
        }, 3000);
    }

    // 返回主页面
    backBtn.addEventListener('click', () => {
        window.location.href = 'popup.html';
    });

    // 导出配置
    exportBtn.addEventListener('click', async () => {
        try {
            const result = await chrome.storage.sync.get(['twoFaKeys']);
            const keys = result.twoFaKeys || [];
            
            if (keys.length === 0) {
                showStatus('没有可导出的数据', 'info');
                return;
            }

            const exportData = {
                version: '1.0',
                exportTime: new Date().toISOString(),
                twoFaKeys: keys
            };

            const jsonString = JSON.stringify(exportData, null, 2);
            
            // 显示预览
            exportPreview.textContent = jsonString;
            exportPreview.style.display = 'block';
            
            // 创建下载链接
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `2fa-backup-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showStatus(`成功导出 ${keys.length} 个密钥配置`, 'success');
        } catch (error) {
            console.error('导出失败:', error);
            showStatus('导出失败: ' + error.message, 'error');
        }
    });

    // 导入配置
    importBtn.addEventListener('click', () => {
        importFile.click();
    });

    importFile.addEventListener('change', async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            let importData;
            
            try {
                importData = JSON.parse(text);
            } catch (parseError) {
                showStatus('文件格式错误，请选择有效的JSON文件', 'error');
                return;
            }

            // 验证数据格式
            if (!importData.twoFaKeys || !Array.isArray(importData.twoFaKeys)) {
                showStatus('文件格式不正确，缺少twoFaKeys数组', 'error');
                return;
            }

            // 获取现有数据
            const result = await chrome.storage.sync.get(['twoFaKeys']);
            const existingKeys = result.twoFaKeys || [];
            
            // 检查重复项并合并数据
            let addedCount = 0;
            let skippedCount = 0;
            const newKeys = [...existingKeys];

            for (const importKey of importData.twoFaKeys) {
                // 检查是否已存在完全相同的配置
                const isDuplicate = existingKeys.some(existing => 
                    existing.keyword === importKey.keyword &&
                    existing.secret === importKey.secret &&
                    existing.notes === importKey.notes
                );

                if (isDuplicate) {
                    skippedCount++;
                    console.log('跳过重复项:', importKey.keyword);
                } else {
                    // 确保有唯一ID
                    if (!importKey.id) {
                        importKey.id = Date.now() + Math.random();
                    }
                    newKeys.push(importKey);
                    addedCount++;
                }
            }

            // 保存合并后的数据
            await chrome.storage.sync.set({ twoFaKeys: newKeys });
            
            let message = `导入完成: 新增 ${addedCount} 个配置`;
            if (skippedCount > 0) {
                message += `, 跳过 ${skippedCount} 个重复项`;
            }
            
            showStatus(message, 'success');
            
        } catch (error) {
            console.error('导入失败:', error);
            showStatus('导入失败: ' + error.message, 'error');
        }
        
        // 清空文件输入
        event.target.value = '';
    });

    // 清除所有数据
    clearAllBtn.addEventListener('click', async () => {
        const confirmed = confirm('确定要清除所有2FA密钥数据吗？\n\n此操作不可恢复，建议先导出备份。');
        
        if (confirmed) {
            try {
                await chrome.storage.sync.clear();
                showStatus('所有数据已清除', 'success');
                
                // 2秒后返回主页面
                setTimeout(() => {
                    window.location.href = 'popup.html';
                }, 2000);
            } catch (error) {
                console.error('清除数据失败:', error);
                showStatus('清除数据失败: ' + error.message, 'error');
            }
        }
    });

    // 页面加载时显示当前数据统计
    window.addEventListener('load', async () => {
        try {
            const result = await chrome.storage.sync.get(['twoFaKeys']);
            const keys = result.twoFaKeys || [];
            
            if (keys.length > 0) {
                showStatus(`当前共有 ${keys.length} 个2FA密钥配置`, 'info');
            }
        } catch (error) {
            console.error('获取数据统计失败:', error);
        }
    });

})();
